2025-09-14 17:17:21,091 - freqtrade.loggers - INFO - Enabling colorized output.
2025-09-14 17:17:21,092 - freqtrade.loggers - INFO - Logfile configured
2025-09-14 17:17:21,092 - freqtrade.loggers - INFO - Verbosity set to 0
2025-09-14 17:17:21,093 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-09-14 17:17:21,093 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-09-14 17:17:21,093 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-09-14 17:17:21,094 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-09-14 17:17:21,094 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-09-14 17:17:21,114 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-09-14 17:17:21,114 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/kraken ...
2025-09-14 17:17:21,116 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-09-14 17:17:21,119 - freqtrade.exchange.check_exchange - INFO - Exchange "kraken" is officially supported by the Freqtrade development team.
2025-09-14 17:17:21,120 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-09-14 17:17:21,120 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-09-14 17:17:21,121 - freqtrade.exchange.exchange - INFO - Using CCXT 4.5.2
2025-09-14 17:17:21,126 - freqtrade.exchange.exchange - INFO - Using Exchange "Kraken"
