2025-09-14 17:17:21,091 - freqtrade.loggers - INFO - Enabling colorized output.
2025-09-14 17:17:21,092 - freqtrade.loggers - INFO - Logfile configured
2025-09-14 17:17:21,092 - freqtrade.loggers - INFO - Verbosity set to 0
2025-09-14 17:17:21,093 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-09-14 17:17:21,093 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-09-14 17:17:21,093 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-09-14 17:17:21,094 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-09-14 17:17:21,094 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-09-14 17:17:21,114 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-09-14 17:17:21,114 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/kraken ...
2025-09-14 17:17:21,116 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-09-14 17:17:21,119 - freqtrade.exchange.check_exchange - INFO - Exchange "kraken" is officially supported by the Freqtrade development team.
2025-09-14 17:17:21,120 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-09-14 17:17:21,120 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-09-14 17:17:21,121 - freqtrade.exchange.exchange - INFO - Using CCXT 4.5.2
2025-09-14 17:17:21,126 - freqtrade.exchange.exchange - INFO - Using Exchange "Kraken"
2025-09-14 17:17:23,273 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Kraken'...
2025-09-14 17:17:23,298 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-09-14 17:17:23,299 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-09-14 17:17:23,300 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-09-14 17:17:23,300 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USD.
2025-09-14 17:17:23,300 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-09-14 17:17:23,301 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-09-14 17:17:23,301 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-09-14 17:17:23,302 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-09-14 17:17:23,302 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-09-14 17:17:23,303 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-09-14 17:17:23,303 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-09-14 17:17:23,304 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-09-14 17:17:23,304 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-09-14 17:17:23,305 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-09-14 17:17:23,306 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-09-14 17:17:23,306 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-09-14 17:17:23,306 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-09-14 17:17:23,307 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USD
2025-09-14 17:17:23,307 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-09-14 17:17:23,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-09-14 17:17:23,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-09-14 17:17:23,309 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-09-14 17:17:23,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-09-14 17:17:23,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-09-14 17:17:23,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-09-14 17:17:23,311 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-09-14 17:17:23,312 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-09-14 17:17:23,312 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-09-14 17:17:23,313 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-09-14 17:17:23,314 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-09-14 17:17:23,315 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-09-14 17:17:23,370 - freqtrade.wallets - INFO - Wallets synced.
2025-09-14 17:17:23,693 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-09-14 17:17:23,800 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 127.0.0.1:8080
2025-09-14 17:17:23,800 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-09-14 17:17:23,815 - uvicorn.error - INFO - Started server process [1]
2025-09-14 17:17:23,815 - uvicorn.error - INFO - Waiting for application startup.
2025-09-14 17:17:23,817 - uvicorn.error - INFO - Application startup complete.
2025-09-14 17:17:23,818 - uvicorn.error - INFO - Uvicorn running on http://127.0.0.1:8080 (Press CTRL+C to quit)
2025-09-14 17:17:23,827 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist VolumePairList from '/freqtrade/freqtrade/plugins/pairlist/VolumePairList.py'...
2025-09-14 17:17:23,828 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-09-14 17:17:24,246 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 20 pairs: ['USDT/USD', 'ETH/USD', 'SOL/USD', 'BTC/USD', 'DOGE/USD', 'XRP/USD', 'PUMP/USD', 'USDC/USD', 'AVAX/USD', 'EUR/USD', 'SUI/USD', 'AVNT/USD', 'LINK/USD', 'ADA/USD', 'PEPE/USD', 'PENGU/USD', 'FARTCOIN/USD', 'WLFI/USD', 'ENA/USD', 'LTC/USD']
2025-09-14 17:17:24,246 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.42s
2025-09-14 17:17:24,247 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-09-14 17:17:24,247 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): buy_rsi = 30
2025-09-14 17:17:24,248 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_short_rsi = 30
2025-09-14 17:17:24,248 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-09-14 17:17:24,249 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): sell_rsi = 70
2025-09-14 17:17:24,249 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): short_rsi = 70
2025-09-14 17:17:24,249 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-09-14 17:17:24,250 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-09-14 17:17:24,250 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-09-14 17:17:24,250 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-09-14 17:17:24,258 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-09-14 17:17:24,259 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `kraken`\n*Stake per trade:* `unlimited USD`\n*Minimum ROI:* `{'60': 0.01, '30': 0.02, '0': 0.04}`\n*Stoploss:* `-0.1`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `SampleStrategy`"}
2025-09-14 17:17:24,259 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USD pairs to buy and sell based on [{'VolumePairList': 'VolumePairList - top 20 volume pairs.'}]"}
2025-09-14 17:17:48,634 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.8', state='RUNNING'
2025-09-14 17:18:48,665 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.8', state='RUNNING'
2025-09-14 17:18:49,976 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-09-14 17:18:49,977 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-09-14 17:18:49,978 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-09-14 17:18:49,979 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-09-14 17:18:49,979 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-09-14 17:18:49,980 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-09-14 17:18:50,054 - uvicorn.error - INFO - Shutting down
2025-09-14 17:18:50,162 - uvicorn.error - INFO - Waiting for application shutdown.
2025-09-14 17:18:50,168 - uvicorn.error - INFO - Application shutdown complete.
2025-09-14 17:18:50,169 - uvicorn.error - INFO - Finished server process [1]
2025-09-14 17:18:50,443 - freqtrade - INFO - SIGINT received, aborting ...
2025-09-14 17:18:58,022 - freqtrade.loggers - INFO - Enabling colorized output.
2025-09-14 17:18:58,023 - freqtrade.loggers - INFO - Logfile configured
2025-09-14 17:18:58,023 - freqtrade.loggers - INFO - Verbosity set to 0
2025-09-14 17:18:58,023 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-09-14 17:18:58,024 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-09-14 17:18:58,024 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-09-14 17:18:58,024 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-09-14 17:18:58,024 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-09-14 17:18:58,038 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-09-14 17:18:58,039 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/kraken ...
2025-09-14 17:18:58,040 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-09-14 17:18:58,045 - freqtrade.exchange.check_exchange - INFO - Exchange "kraken" is officially supported by the Freqtrade development team.
2025-09-14 17:18:58,045 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-09-14 17:18:58,046 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-09-14 17:18:58,046 - freqtrade.exchange.exchange - INFO - Using CCXT 4.5.2
2025-09-14 17:18:58,051 - freqtrade.exchange.exchange - INFO - Using Exchange "Kraken"
2025-09-14 17:19:00,147 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Kraken'...
2025-09-14 17:19:00,164 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-09-14 17:19:00,165 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-09-14 17:19:00,166 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-09-14 17:19:00,166 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USD.
2025-09-14 17:19:00,166 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-09-14 17:19:00,166 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-09-14 17:19:00,167 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-09-14 17:19:00,167 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-09-14 17:19:00,167 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-09-14 17:19:00,168 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-09-14 17:19:00,168 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-09-14 17:19:00,168 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-09-14 17:19:00,169 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-09-14 17:19:00,169 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-09-14 17:19:00,169 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-09-14 17:19:00,170 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-09-14 17:19:00,170 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-09-14 17:19:00,171 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USD
2025-09-14 17:19:00,171 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-09-14 17:19:00,171 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-09-14 17:19:00,172 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-09-14 17:19:00,172 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-09-14 17:19:00,173 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-09-14 17:19:00,173 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-09-14 17:19:00,173 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-09-14 17:19:00,174 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-09-14 17:19:00,174 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-09-14 17:19:00,174 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-09-14 17:19:00,174 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-09-14 17:19:00,175 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-09-14 17:19:00,175 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-09-14 17:19:00,203 - freqtrade.wallets - INFO - Wallets synced.
2025-09-14 17:19:00,397 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-09-14 17:19:00,522 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 127.0.0.1:8080
2025-09-14 17:19:00,523 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-09-14 17:19:00,538 - uvicorn.error - INFO - Started server process [1]
2025-09-14 17:19:00,539 - uvicorn.error - INFO - Waiting for application startup.
2025-09-14 17:19:00,540 - uvicorn.error - INFO - Application startup complete.
2025-09-14 17:19:00,540 - uvicorn.error - INFO - Uvicorn running on http://127.0.0.1:8080 (Press CTRL+C to quit)
2025-09-14 17:19:00,550 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist VolumePairList from '/freqtrade/freqtrade/plugins/pairlist/VolumePairList.py'...
2025-09-14 17:19:00,550 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-09-14 17:19:01,133 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 20 pairs: ['USDT/USD', 'ETH/USD', 'SOL/USD', 'BTC/USD', 'DOGE/USD', 'XRP/USD', 'PUMP/USD', 'USDC/USD', 'AVAX/USD', 'EUR/USD', 'SUI/USD', 'AVNT/USD', 'LINK/USD', 'ADA/USD', 'PEPE/USD', 'PENGU/USD', 'FARTCOIN/USD', 'WLFI/USD', 'ENA/USD', 'LTC/USD']
2025-09-14 17:19:01,135 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.58s
2025-09-14 17:19:01,137 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-09-14 17:19:01,138 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): buy_rsi = 30
2025-09-14 17:19:01,139 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_short_rsi = 30
2025-09-14 17:19:01,139 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-09-14 17:19:01,140 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): sell_rsi = 70
2025-09-14 17:19:01,140 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): short_rsi = 70
2025-09-14 17:19:01,141 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-09-14 17:19:01,141 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-09-14 17:19:01,142 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-09-14 17:19:01,142 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-09-14 17:19:01,155 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-09-14 17:19:01,155 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `kraken`\n*Stake per trade:* `unlimited USD`\n*Minimum ROI:* `{'60': 0.01, '30': 0.02, '0': 0.04}`\n*Stoploss:* `-0.1`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `SampleStrategy`"}
2025-09-14 17:19:01,156 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USD pairs to buy and sell based on [{'VolumePairList': 'VolumePairList - top 20 volume pairs.'}]"}
2025-09-14 17:19:25,678 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.8', state='RUNNING'
2025-09-14 17:19:50,829 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-09-14 17:19:50,830 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-09-14 17:19:50,830 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-09-14 17:19:50,831 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-09-14 17:19:50,832 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-09-14 17:19:50,832 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-09-14 17:19:50,905 - uvicorn.error - INFO - Shutting down
2025-09-14 17:19:51,011 - uvicorn.error - INFO - Waiting for application shutdown.
2025-09-14 17:19:51,014 - uvicorn.error - INFO - Application shutdown complete.
2025-09-14 17:19:51,015 - uvicorn.error - INFO - Finished server process [1]
2025-09-14 17:19:51,272 - freqtrade - INFO - SIGINT received, aborting ...
2025-09-14 17:19:53,489 - freqtrade.loggers - INFO - Enabling colorized output.
2025-09-14 17:19:53,489 - freqtrade.loggers - INFO - Logfile configured
2025-09-14 17:19:53,490 - freqtrade.loggers - INFO - Verbosity set to 0
2025-09-14 17:19:53,490 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-09-14 17:19:53,490 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-09-14 17:19:53,491 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-09-14 17:19:53,491 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-09-14 17:19:53,491 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-09-14 17:19:53,505 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-09-14 17:19:53,505 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/kraken ...
2025-09-14 17:19:53,506 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-09-14 17:19:53,509 - freqtrade.exchange.check_exchange - INFO - Exchange "kraken" is officially supported by the Freqtrade development team.
2025-09-14 17:19:53,509 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-09-14 17:19:53,510 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-09-14 17:19:53,510 - freqtrade.exchange.exchange - INFO - Using CCXT 4.5.2
2025-09-14 17:19:53,515 - freqtrade.exchange.exchange - INFO - Using Exchange "Kraken"
2025-09-14 17:19:55,547 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Kraken'...
2025-09-14 17:19:55,555 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-09-14 17:19:55,556 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-09-14 17:19:55,556 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-09-14 17:19:55,557 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USD.
2025-09-14 17:19:55,557 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-09-14 17:19:55,557 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-09-14 17:19:55,557 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-09-14 17:19:55,558 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-09-14 17:19:55,558 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-09-14 17:19:55,558 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-09-14 17:19:55,558 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-09-14 17:19:55,559 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-09-14 17:19:55,559 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-09-14 17:19:55,559 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-09-14 17:19:55,559 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-09-14 17:19:55,560 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-09-14 17:19:55,560 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-09-14 17:19:55,560 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USD
2025-09-14 17:19:55,560 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-09-14 17:19:55,560 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-09-14 17:19:55,561 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-09-14 17:19:55,561 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-09-14 17:19:55,561 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-09-14 17:19:55,561 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-09-14 17:19:55,562 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-09-14 17:19:55,562 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-09-14 17:19:55,562 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-09-14 17:19:55,562 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-09-14 17:19:55,562 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-09-14 17:19:55,563 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-09-14 17:19:55,563 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-09-14 17:19:55,581 - freqtrade.wallets - INFO - Wallets synced.
2025-09-14 17:19:56,042 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
